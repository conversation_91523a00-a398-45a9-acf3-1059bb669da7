import React from "react";
import {
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Button,
  Row,
  Col,
  Tooltip,
} from "antd";
import {
  MinusCircleOutlined,
  PlusOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { FormInstance } from "antd/es/form";

interface IntervalPriceFormProps {
  form: FormInstance;
}

const IntervalPriceForm: React.FC<IntervalPriceFormProps> = ({ form }) => {
  const { t } = useTranslation();

  const priceControls = (
    <div className="horizontal-controls-group">
      <Tooltip
        title={t("internationalPriceManagement.actions.batchPriceIncrease")}
      >
        <Button
          type="text"
          icon={<ArrowUpOutlined />}
          size="small"
          className="control-button increase-button"
          onClick={(e) => {
            e.stopPropagation();
            // adjustIntervalPrice(index, parentId, 1);
          }}
        />
      </Tooltip>
      <Tooltip
        title={t("internationalPriceManagement.actions.batchPriceDecrease")}
      >
        <Button
          type="text"
          icon={<ArrowDownOutlined />}
          size="small"
          className="control-button decrease-button"
          onClick={(e) => {
            e.stopPropagation();
            // adjustIntervalPrice(index, parentId, -1);
          }}
        />
      </Tooltip>
    </div>
  );

  return (
    <div className="other-fees-section">
      <Form.List name="intervalprice">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <div key={key} className="interval-price-item">
                <Row gutter={[12, 0]} align="middle">
                  <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                    <div className="common-label">
                      {t(
                        "internationalPriceManagement.actionModal.fields.densityRange"
                      )}
                    </div>
                    <div
                      style={{
                        display: "flex",
                        gap: "8px",
                        alignItems: "center",
                      }}
                    >
                      <Form.Item
                        {...restField}
                        name={[name, "densitylvalue"]}
                        style={{ margin: 0, flex: 1 }}
                        rules={[
                          {
                            required: true,
                            message: t(
                              "internationalPriceManagement.actionModal.validation.inputMinDensity"
                            ),
                          },
                        ]}
                      >
                        <InputNumber
                          className="input-default"
                          min={0}
                          precision={0}
                          placeholder={t(
                            "internationalPriceManagement.actionModal.placeholders.minDensity"
                          )}
                        />
                      </Form.Item>
                      <span>-</span>
                      <Form.Item
                        {...restField}
                        name={[name, "densityrvalue"]}
                        style={{ margin: 0, flex: 1 }}
                        rules={[
                          {
                            required: true,
                            message: t(
                              "internationalPriceManagement.actionModal.validation.inputMaxDensity"
                            ),
                          },
                        ]}
                      >
                        <InputNumber
                          className="input-default"
                          min={0}
                          precision={0}
                          placeholder={t(
                            "internationalPriceManagement.actionModal.placeholders.maxDensity"
                          )}
                        />
                      </Form.Item>
                    </div>
                  </Col>
                  <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                    <Form.Item
                      {...restField}
                      name={[name, "q100"]}
                      label={t(
                        "internationalPriceManagement.actionModal.fields.q100Price"
                      )}
                    >
                      <InputNumber
                        className="input-default"
                        min={0}
                        precision={1}
                        placeholder={t(
                          "internationalPriceManagement.actionModal.placeholders.inputQ100Price"
                        )}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                    <Form.Item
                      {...restField}
                      name={[name, "q300"]}
                      label={t(
                        "internationalPriceManagement.actionModal.fields.q300Price"
                      )}
                    >
                      <InputNumber
                        className="input-default"
                        min={0}
                        precision={1}
                        placeholder={t(
                          "internationalPriceManagement.actionModal.placeholders.inputQ300Price"
                        )}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                    <Form.Item
                      {...restField}
                      name={[name, "q500"]}
                      label={t(
                        "internationalPriceManagement.actionModal.fields.q500Price"
                      )}
                    >
                      <InputNumber
                        className="input-default"
                        min={0}
                        precision={1}
                        placeholder={t(
                          "internationalPriceManagement.actionModal.placeholders.inputQ500Price"
                        )}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                    <Form.Item
                      {...restField}
                      name={[name, "q1000"]}
                      label={t(
                        "internationalPriceManagement.actionModal.fields.q1000Price"
                      )}
                    >
                      <InputNumber
                        className="input-default"
                        min={0}
                        precision={1}
                        placeholder={t(
                          "internationalPriceManagement.actionModal.placeholders.inputQ1000Price"
                        )}
                      />
                    </Form.Item>
                  </Col>
                  {priceControls}
                  <MinusCircleOutlined
                    className="icon-remove"
                    onClick={() => remove(name)}
                    style={{
                      fontSize: "16px",
                      color: "#ff4d4f",
                      marginLeft: "10px",
                    }}
                  />
                </Row>
              </div>
            ))}
            <Button
              type="dashed"
              onClick={() => add()}
              block
              icon={<PlusOutlined />}
              className="btn-dashed"
              style={{ marginTop: "16px" }}
            >
              {t(
                "internationalPriceManagement.actionModal.buttons.addIntervalPrice"
              )}
            </Button>
          </>
        )}
      </Form.List>
    </div>
  );
};

export default IntervalPriceForm;
