import React, { useEffect } from "react";
import { Modal, Form, Button, message } from "antd";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { InternationalPrice } from "../types";
import { updateInternationalPrice } from "../services";
import { sortCabinReportByDate } from "@/utils/util";
import IntervalPriceForm from "./IntervalPriceForm";
import "./index.less";

interface IntervalPriceModalProps {
  visible: boolean;
  onCancel: () => void;
  record: InternationalPrice | null;
  onSuccess: () => void;
}

const IntervalPriceModal: React.FC<IntervalPriceModalProps> = ({
  visible,
  onCancel,
  record,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && record) {
      const IntervalPriceData = record.intervalprice || [];
      // const formattedIntervalPriceData = IntervalPriceData.map((cabin: any) => ({
      //   ...cabin,
      //   date: cabin.date ? dayjs(cabin.date) : null,
      //   transferdate: cabin.transferdate ? dayjs(cabin.transferdate) : null,
      //   pricechanges: cabin.pricechanges || [],
      //   specialprice: cabin.specialprice || [],
      // }));

      form.setFieldsValue({
        intervalprice: IntervalPriceData,
      });
    }
  }, [visible, record, form]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      // // 处理舱报信息，转换日期为时间戳并按日期排序
      // const updatedCabinReport = values.cabinreport
      //   ? sortCabinReportByDate(
      //       values.cabinreport.map((cabin: any) => ({
      //         ...cabin,
      //         date: formatDateToTimestamp(cabin.date),
      //         transferdate: cabin?.transferdate
      //           ? formatDateToTimestamp(cabin?.transferdate)
      //           : undefined,
      //         pricechanges: cabin.pricechanges || [],
      //         specialprice: cabin.specialprice || [],
      //       }))
      //     )
      //   : [];
      console.log("更新后的信息:", values);

      if (!record) {
        message.error("记录信息不存在");
        return;
      }

      const updateData = {
        ...record,
        intervalprice: values?.intervalprice || [],
      };

      await updateInternationalPrice(updateData);
      message.success(
        t("internationalPriceManagement.messages.cabinReportUpdateSuccess")
      );
      onSuccess();
      onCancel();
    } catch (error) {
      console.error("更新密度价格信息失败:", error);
      message.error(
        t("internationalPriceManagement.messages.cabinReportUpdateFailed")
      );
    }
  };

  return (
    <Modal
      title={t("internationalPriceManagement.intervalPriceEdit.title")}
      open={visible}
      onCancel={onCancel}
      width={1200}
      centered
      className="interval-price-edit-modal"
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {t("common.cancel")}
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          {t("common.save")}
        </Button>,
      ]}
      destroyOnClose
    >
      <Form form={form} layout="vertical">
        <IntervalPriceForm form={form} />
      </Form>
    </Modal>
  );
};

export default IntervalPriceModal;
