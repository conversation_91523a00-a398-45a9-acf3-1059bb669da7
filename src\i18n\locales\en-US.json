{"common": {"confirm": "Confirm", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "reset": "Reset", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "loading": "Loading...", "success": "Operation successful", "error": "Operation failed", "warning": "Warning", "info": "Info", "yes": "Yes", "no": "No", "close": "Close", "refresh": "Refresh", "export": "Export", "import": "Import", "view": "View", "detail": "Detail", "operation": "Operation", "status": "Status", "createTime": "Create Time", "updateTime": "Update Time", "remark": "Remark", "total": "Total {{count}} items", "page": "Page {{current}} of {{total}}", "createinquiry": "New Inquiry", "quickinquiry": "Quick Inquiry", "all": "All", "notquoted": "Not Quoted", "closed": "Closed", "notclosed": "Not Closed", "shippingmethod": "Transport type", "cargotype": "Cargo Type", "companyname": "Customer Name", "inquirer": "Inquirer", "originport": "Origin Port", "destinationport": "Destination Port", "grossweight": "Gross Weight", "cargovolume": "Cargo Volume", "singlemaxweight": "Single Max Weight", "specialcargo": "Special Cargo", "manualquotation": "Manual Quotation", "systemUpdate": "System Update", "updateDetected": "A new version is available. Please refresh the page for the best experience.", "refreshNow": "Refresh Now", "remindLater": "Re<PERSON> Later", "specialmanagement": "Special Quotation Management", "inquiry": "Inquiry", "successd": "Success", "failure": "Failure", "unknownerror": "Unknown Error", "basicinformation": "Basic Information", "companynamein": "Please enter the customer name", "inquirytimein": "Please select the inquiry time", "inquirerin": "Please enter the inquirer", "tradeterms": "Incoterm(EXW/FOB/OTHER)", "cargoinformation": "Cargo Information", "shippingrequirements": "Shipping Requirements", "specialrequirements": "Special Requirements", "user": "User", "checkNetworkConnection": "Please check network connection", "submitError": "Submit error:", "formValidationError": "Form validation error:", "formValidationFailed": "Form validation failed, please check input", "pleaseSelectInquiryTime": "Please select inquiry time", "pleaseSelectOriginPort": "Please select origin port", "pleaseSelectDestinationPort": "Please select destination port", "pleaseInputGrossWeight": "Please input gross weight", "pleaseSelectShipmentDate": "Please select shipment date"}, "layout": {"systemTitle": "Quote Management System", "profile": "Profile", "changePassword": "Change Password", "logout": "Logout", "deactivateAccount": "Deactivate Account", "logoutConfirm": "Are you sure you want to logout?", "logoutContent": "You need to login again after logout", "deactivateConfirm": "Are you sure you want to deactivate your account?", "deactivateContent": "After deactivation, your account will be permanently deleted and all data will be unrecoverable. Please proceed with caution!", "confirmDeactivate": "Confirm Deactivation", "logoutSuccess": "Successfully logged out", "deactivateSuccess": "Account successfully deactivated"}, "menu": {"quotation": "Quotation Management", "inquiryTime": "Inquiry Time", "supplyPrice": "Supply Price Filter", "manualQuotation": "Special Quotation", "aiQuotation": "AI Smart Quotation", "IntelligentyQuotation": "Intelligent Quotation", "personalQuotation": "Personal Quotation", "priceManagement": "Price Management", "domesticPrice": "Domestic Price", "internationalPrice": "International Price", "dataManagement": "Basic Data Management", "airlineManagement": "Airline Management", "harborManagement": "Harbor Management", "organizationManage": "Organization Management", "packageTypeManagement": "Package Type Management", "specialItemsManagement": "Special Items Management", "shipmentPlaceManagement": "Shipment Place Management", "supplierManagement": "Supplier Management", "miscellaneousFeeManagement": "Miscellaneous Fee Management"}, "form": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "password": "Password must be at least 6 characters", "confirmPassword": "Passwords do not match", "pleaseInput": "Please input", "pleaseSelect": "Please select", "inputPlaceholder": "Please input {{field}}", "selectPlaceholder": "Please select {{field}}"}, "table": {"noData": "No data", "actions": "Actions", "serialNumber": "No.", "total": "Total {{total}} records"}, "login": {"title": "User Login", "account": "Account", "accountPlaceholder": "Please enter the account.", "email": "Email", "password": "Password", "login": "<PERSON><PERSON>", "LoginIn": "Login in...", "register": "Register", "forgotPassword": "Forgot Password?", "loginSuccess": "Login successful", "loginFailed": "<PERSON><PERSON> failed. Please check the username and password", "loginRequestFailed": "The login request failed. Please try again later", "emailRequired": "Please enter email", "passwordRequired": "Please enter password", "invalidEmail": "Please enter a valid email format", "passwordTooShort": "Password must be at least 6 characters", "RegistrationSuccess": "Registration successful. Please log in", "RegistrationFailed": "Registration failed. Please try again later", "inputRequired": "Please enter the account and password", "noAccount": "No account yet?", "RegisterNow": "Register now"}, "register": {"title": "User Registration", "emailLabel": "Email", "emailRequired": "Please enter your email", "emailInvalid": "Please enter a valid email address", "fullnameLabel": "Name", "fullnameMaxLength": "Name length cannot exceed 50 characters", "fullnamePlaceholder": "Please enter your name", "telephoneLabel": "Phone", "telephonePlaceholder": "Please enter phone", "telephoneInvalid": "Please enter a valid phone number", "passwordLabel": "Password", "passwordRequired": "Please enter password", "passwordMaxLength": "Password length cannot exceed 20 characters", "confirmPasswordLabel": "Confirm Password", "confirmPasswordRequired": "Please confirm password", "confirmPasswordMismatch": "The two passwords do not match", "verifyCodeLabel": "Verification Code", "verifyCodeRequired": "Please enter verification code", "verifyCodeLength": "Verification code is 6 digits", "sendCode": "Get Verification Code", "sendCodeCountdown": "Resend after {{countdown}}s"}, "profile": {"title": "Profile Management", "basicInfo": "Basic Information", "email": "Email", "name": "Name", "phone": "Phone", "company": "Company", "department": "Department", "position": "Position", "updateSuccess": "Profile updated successfully", "updateFailed": "Profile update failed"}, "password": {"title": "Change Password", "oldPassword": "Old Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "changeSuccess": "Password changed successfully", "changeFailed": "Password change failed", "oldPasswordRequired": "Please enter old password", "newPasswordRequired": "Please enter new password", "confirmPasswordRequired": "Please confirm new password", "passwordMismatch": "Passwords do not match", "passwordTooShort": "Password must be at least 6 characters"}, "verification": {"title": "Identity Verification", "sendCode": "Get Verification Code", "inputCode": "Enter Verification Code", "codeSent": "Verification code has been sent to your email", "codeSendFailed": "Failed to send verification code", "codeRequired": "Please enter 6-digit verification code", "codeInvalid": "Invalid verification code", "resendAfter": "Resend after {{seconds}} seconds", "sending": "Sending...", "emailHint": "Please enter the verification code sent to your email ({{email}}) to confirm account deactivation:", "emailContent": "Please enter a valid email address"}, "supplyPrice": {"title": "Supply Price Filter", "inquiryInfo": "Inquiry Information", "selectedInquiry": "Inquiry Selected", "noInquirySelected": "No Inquiry Selected", "pleaseSelectInquiry": "Please select an inquiry from the inquiry management page first", "inquiryNumber": "Inquiry Number", "filterConditions": "Filter Conditions", "requiredFilters": "Required Info", "coreFilters": "Core Filters", "basicFilters": "Basic Info", "advancedFilters": "Advanced Filter Conditions", "expandAdvanced": "Expand Advanced Filters", "collapseAdvanced": "Collapse Advanced Filters", "applyFilter": "Apply Filter", "reset": "Reset", "selectAirline": "Select Airline", "selectOriginPorts": "Select Origin Ports", "selectDates": "Select Departure Dates", "selectTransferType": "Select Flight Type", "transferTypes": {"direct": "Direct", "transfer": "Transfer"}, "lowestPrice": "Lowest Price", "fastestTransit": "Fastest Transit", "noDataMessage": "No data available, please adjust filter conditions", "loadingMessage": "Loading...", "getSupplyPriceListFailed": "Failed to get supply price list", "queryFailed": "Query failed, please check network connection", "pleaseCompleteRequired": "Please complete required fields before applying filter", "fields": {"originPort": "Origin Port", "destinationPort": "Destination Port", "grossWeight": "Gross Weight", "goodsVolume": "Goods Volume", "singleMaxWeight": "Single Max Weight", "freightMethod": "Transport Type", "goodsType": "Goods Type", "shippedPlace": "Pick Up Place", "cargoSize": "Dimension", "cargoLength": "Cargo Length", "cargoWidth": "<PERSON><PERSON>", "cargoHeight": "Cargo Height", "isBrand": "Brand Goods", "ensureCabin": "Space Priority", "requireETD": "Require ETD", "shipmentDate": "Shipment Date", "packageType": "Package Type", "specialCargo": "Special Cargo", "NumPackages": "Number of Packages", "sanctioned": "Sanctioned", "shippingDate": "Shipping Date"}, "placeholders": {"selectOriginPort": "Please select origin port", "selectDestinationPort": "Please select destination port", "inputGrossWeight": "Please input gross weight", "inputGoodsVolume": "Please input goods volume", "inputSingleMaxWeight": "Please input single max weight", "inputnumpackages": "Please enter number of packages", "selectFreightMethod": "Please select transport type", "selectGoodsType": "Please select goods type", "inputShippedPlace": "Please input pick up place", "inputCargoLength": "Please input cargo length", "inputCargoWidth": "Please input cargo width", "inputCargoHeight": "Please input cargo height", "selectShipmentDate": "Please select shipment date", "selectPackageType": "Please select package type", "selectSpecialCargo": "Please select special cargo", "inputcompanyname": "Please enter customer name", "inputinquirer": "Please enter inquirer", "inputtradeterms": "Please enter incoterme"}, "validation": {"originPortRequired": "Please select origin port", "destinationPortRequired": "Please select destination port", "grossWeightRequired": "Please input gross weight", "shipmentDateRequired": "Please select shipment date"}, "freightMethods": {"air": "Air Freight", "sea": "Sea Freight", "rail": "Rail Freight"}, "goodsTypes": {"fcl": "<PERSON> (FCL)", "lcl": "Consolidated Cargo (LCL)"}, "sectionTitles": {"cargoSize": "Cargo Size", "specialOptions": "Special Options"}}, "priceCard": {"features": {"cabin": "Space Priority", "transfer": "Transfer", "packaging": "Packaging Fee", "specialItems": "Special Items"}, "fields": {"unitPrice": "Unit Price", "transitTime": "Transit Time", "nearestFlight": "Nearest Flight", "totalPrice": "Total Price", "generateQuote": "Generate Quote", "flightFrequency": "Flight Frequency", "weightCharge": "Chargeable Weight", "etdCompliant": "ETD Compliant", "densityRange": "Density Range", "canEnsureCabin": "Space Priority", "sizeLimit": "Size Limit", "priceGradient": "<PERSON> Gradient", "packagingCharges": "Packaging Charges", "specialItemCharges": "Special Item Charges", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "yes": "Yes", "no": "No", "canEnsure": "Can Ensure", "cannotEnsure": "Cannot Ensure", "days": "days"}, "weightNote": "(Within {{weight}}kg)", "placeholders": {"length": "Length", "width": "<PERSON><PERSON><PERSON>", "height": "Height"}}, "quotationTable": {"columns": {"inquiryTime": "Inquiry Time", "freightMethod": "Transport Type", "shippedPlace": "Pick Up Place", "originPort": "Origin Port", "destinationPort": "Destination Port", "goodsType": "Goods Type", "grossWeight": "Gross Weight", "goodsVolume": "Goods Volume", "singleMaxWeight": "Single Max Weight", "cargoSize": "Dimension", "companyName": "Customer Name", "inquirer": "Inquirer", "isBrand": "Brand Goods", "ensureCabin": "Ensure Cabin", "requireETD": "Require ETD", "shipmentDate": "Shipment Date", "packageType": "Package Type", "specialCargo": "Special Cargo", "status": "Status", "actions": "Actions"}, "filters": {"selectShippedPlace": "Please select pick up place", "selectOriginPort": "Please select origin port", "selectDestinationPort": "Please select destination port", "inputCompanyName": "Input customer name", "inputInquirer": "Input inquirer"}, "status": {"notQuoted": "Not Quoted", "deal": "Deal", "noDeal": "No Deal", "unknown": "Unknown"}, "actions": {"edit": "Edit", "delete": "Delete", "queryPrice": "Query Price", "manualQuote": "Manual Quote"}, "confirmDelete": {"title": "Are you sure to delete this inquiry?", "description": "This operation is irreversible, please proceed with caution.", "okText": "Confirm", "cancelText": "Cancel"}, "goodsTypes": {"fcl": "FCL", "lcl": "LCL"}, "booleanValues": {"yes": "Yes", "no": "No"}, "messages": {"deleteSuccess": "Inquiry deleted successfully", "deleteFailed": "Delete failed", "getListFailed": "Failed to get inquiry list", "manualQuoteSuccess": "Added to special quotation list, please check in Special Quotation Management!", "getDepartmentUsersFailed": "Failed to get department users list:"}, "placeholders": {"selectDepartmentMember": "Select department member", "allDepartmentMembers": "All department members"}, "noValue": "None", "noData": "-"}, "aiCabinExtractor": {"buttonText": "AI Extract Cabin Info", "modalTitle": "AI Smart Extract Cabin Report Information", "extractButton": "Extract Information", "inputLabel": "Please input cabin report text:", "inputPlaceholder": "Please paste or input cabin report text, AI will automatically identify and extract relevant data...\n\n", "tipsTitle": "Usage Tips", "tips": {"tip1": "Supports recognition of flight date, weight limit, volume limit and other basic information", "tip2": "Automatically extracts density range, price adjustments, special price information", "tip3": "Can identify special requirements such as parts only, single inquiry, etc.", "tip4": "Extracted information can be further manually adjusted and refined"}, "messages": {"inputRequired": "Please input cabin report text", "extractSuccess": "Cabin information extraction successful!", "extractFailed": "Extraction failed, please try again"}, "errors": {"aiResponseError": "Parsing failed."}}, "emailExtractModal": {"title": "Quick Extract Inquiry Information", "steps": {"inputContent": "Input Content", "confirmInfo": "Confirm Information", "createQuotation": "Create Quotation"}, "stepCards": {"inputContentTitle": "Please Input Extract Content", "confirmInfoTitle": "Confirm Extracted Information", "createQuotationTitle": "Create Quotation"}, "alerts": {"inputTip": "Tip", "inputDescription": "Please paste the email content containing inquiry information into the text box below, AI will automatically extract relevant freight information.", "confirmTitle": "Information Extraction Completed", "confirmDescription": "Please check if the extracted information below is correct, you can edit and modify directly.", "multiplePortsDetected": "Multiple destination ports detected, {{count}} inquiry records will be created", "portListLabel": "Port List:", "editFirstRecordTip": "You can edit the information of the first record below, other records will use the same basic information."}, "placeholders": {"emailContent": "Please paste email content or inquiry information...", "companyName": "Customer Name", "inquirer": "Inquirer", "shippedPlace": "Pick Up Place", "originPort": "Origin Port", "destinationPort": "Destination Port", "grossWeight": "Gross Weight", "goodsVolume": "Volume", "goodsNumber": "Number of Packages", "cargoLength": "Length", "cargoWidth": "<PERSON><PERSON><PERSON>", "cargoHeight": "Height", "packageType": "Package Type", "specialCargo": "Special Cargo Requirements"}, "formLabels": {"basicInfo": "Basic Information", "cargoInfo": "Cargo Information", "companyName": "Customer Name", "inquirer": "Inquirer", "shippedPlace": "Pick Up Place", "originPort": "Origin Port", "destinationPort": "Destination Port", "grossWeight": "Gross Weight (kg)", "goodsVolume": "Volume (CBM)", "goodsNumber": "Number of Packages", "cargoLength": "Length (cm)", "cargoWidth": "Width (cm)", "cargoHeight": "Height (cm)", "packageType": "Package Type", "specialCargo": "Special Cargo"}, "buttons": {"cancel": "Cancel", "startExtract": "Start Extracting Information", "extracting": "AI is extracting information...", "backToPrevious": "Back to Previous Step", "confirmAndCreate": "Confirm Information and Create Quotation", "creating": "Creating Quotation..."}, "loadingTexts": {"aiAnalyzing": "AI is Analyzing Content", "aiAnalyzingDesc": "Using AI to extract freight inquiry information from your content, please wait...", "analyzingProgress": "Analyzing content and extracting key information...", "creatingQuotation": "Creating Quotation", "creatingQuotationDesc": "The system is creating a quotation based on the extracted information, please wait...", "creatingProgress": "About to complete quotation creation..."}, "messages": {"inputRequired": "Please input extract content", "extractSuccess": "Information extraction successful!", "extractFailed": "Information extraction failed, please try again", "createSuccess": "Quotation created successfully", "createMultipleSuccess": "Successfully created {{count}} inquiry records", "createFailed": "Failed to create quotation, please try again", "formValidationFailed": "Please check form information", "apiKeyInvalid": "DeepSeek API Key is invalid or not set, please check configuration", "parseFormatFailed": "AI response format parsing failed, please try again", "extractFailedWithError": "Extraction failed: {{error}}"}, "validationMessages": {"selectOriginPort": "Please select origin port", "selectDestinationPort": "Please select destination port", "inputGrossWeight": "Please input gross weight", "selectShipmentDate": "Please select shipment date"}, "formPlaceholders": {"length": "Length", "width": "<PERSON><PERSON><PERSON>", "height": "Height"}, "console": {"startFirstRound": "Starting first round AI conversation...", "firstRoundResult": "First round AI response:", "startSecondRound": "Starting second round AI conversation...", "secondRoundResult": "Second round AI response:", "extractFailed": "AI information extraction failed:", "createFailed": "Failed to create inquiry:", "convertShippedPlaceError": "Error converting shipped place \"{{place}}\" to pinyin:", "parseSecondRoundFailed": "Failed to parse second round AI response:"}, "errors": {"firstRoundFormatError": "First round AI interface returned format error", "secondRoundFormatError": "Second round AI interface returned format error", "aiResponseFormatIncorrect": "AI response format is incorrect", "parseSecondRoundFailed": "Failed to parse second round AI response", "parseKeyword": "parse"}}, "internationalPriceManagement": {"title": "International Price Management", "newPrice": "New Price", "selectDepartmentMember": "Select Department Member", "user": "User", "clearAllFilters": "Clear Filters", "columns": {"updateDate": "Update Date", "airlineName": "Airline", "originPort": "Origin Port", "destinationPort": "Destination Port", "originSchedules": "Origin Schedules", "transferPort": "Transfer Port", "transferSchedules": "Transfer Schedules", "supplier": "Supplier", "mPrice": "M", "nPrice": "N", "q45Price": "Q45", "q100Price": "Q100", "q300Price": "Q300", "q500Price": "Q500", "q1000Price": "Q1000e", "subQ45Price": "Sub Q45", "subQ100Price": "Sub Q100", "densityPriceRange": "Density Price Range", "densityRange": "Density(kg/m³)", "lengthLimit": "Length Limit", "widthLimit": "Width Limit", "heightLimit": "Height Limit", "packageCharges": "Package Charges", "specialCharges": "Special Item Charges", "isEffective": "Is Effective", "effectiveDate": "Effective Date", "cabinReport": "Cabin Report", "actions": "Actions", "id": "ID"}, "filters": {"selectAirline": "Please select airline", "selectOriginPort": "Please select origin port", "selectDestinationPort": "Please select destination port", "inputSupplierName": "Input supplier name", "selectSupplier": "Select supplier"}, "schedules": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "status": {"effective": "Effective", "ineffective": "Ineffective"}, "transfer": {"yes": "Transfer", "no": "Direct"}, "actions": {"save": "Save", "cancel": "Cancel", "viewDetail": "View Detail", "copy": "Copy", "edit": "Edit", "delete": "Delete", "priceIncrease": "Price +1", "priceDecrease": "Price -1", "batchPriceIncrease": "Q100-Q1000 Prices +1", "batchPriceDecrease": "Q100-Q1000 Prices -1"}, "confirmDelete": {"title": "Delete Price", "description": "This operation will delete this price, continue?", "okText": "Confirm", "cancelText": "Cancel"}, "placeholders": {"scheduleFormat": "Format: D+digits1-7, e.g. D123"}, "cabinReport": {"detailTitle": "Cabin Report Details", "totalCount": "Total {{count}} items", "unknownDate": "Unknown Date", "unknown": "Unknown", "badges": {"limit": "Limit", "special": "Special", "parts": "Parts", "inquiry": "Inquiry"}, "tags": {"limit": "L", "special": "S"}, "labels": {"densityRange": "Density Range", "limitations": "Limitations", "weight": "Weight", "volume": "Volume", "priceChanges": "Price Changes", "specialPrice": "Special Price", "density": "Density"}}, "messages": {"priceUpdateSuccess": "Price updated successfully", "priceUpdateFailed": "Price update failed", "priceDeleteSuccess": "Price deleted successfully", "deleteFailed": "Delete failed", "getListFailed": "Failed to get international price list", "formValidationFailed": "Form validation failed", "getDepartmentUsersFailed": "Failed to get department users list", "priceIncreaseSuccess": "Price increased successfully", "priceDecreaseSuccess": "Price decreased successfully", "quickAdjustFailed": "Quick price adjustment failed", "pleaseSelectRows": "Please select records to modify", "pleaseInputAdjustValue": "Please input valid price (cannot be less than 0)", "batchUpdateSuccess": "Batch setting successful, {{count}} {{type}} records modified", "batchUpdateFailed": "Batch update failed", "cabinReportUpdateSuccess": "Cabin report updated successfully", "cabinReportUpdateFailed": "Cabin report update failed", "intervalDeleteSuccess": "Density price interval deleted successfully", "clearFiltersSuccess": "All filters have been cleared"}, "densityPrice": {"detailTitle": "Density Price Range Details", "totalCount": "Total {{count}} intervals"}, "batchEdit": {"title": "<PERSON>ch Edit", "batchMode": "Batch Edit Mode", "selectedCount": "{{count}} records selected", "batchEditButton": "<PERSON>ch Edit", "cancel": "Cancel", "confirm": "Confirm Changes", "description": "Batch edit will be applied to the selected {{count}} records. Only fill in the fields you want to modify:", "pleaseSelectAtLeastOneField": "Please fill in at least one field to modify", "batchUpdateSuccess": "Successfully batch updated {{count}} records", "batchUpdateFailed": "Batch update failed, please try again", "fields": {"mPrice": "<PERSON> Price", "nPrice": "N Price", "q45Price": "Q45 Price", "subQ45Price": "Sub Q45 Price", "subQ100Price": "Sub Q100 Price", "originSchedules": "Origin Schedules", "transferSchedules": "Transfer Schedules", "isEffective": "Is Effective"}, "placeholders": {"inputNewMPrice": "Enter new M price", "inputNewNPrice": "Enter new N price", "inputNewQ45Price": "Enter new Q45 price", "inputNewSubQ45Price": "Enter new Sub Q45 price", "inputNewSubQ100Price": "Enter new Sub Q100 price", "scheduleFormat": "Format: D+digits1-7, e.g. D123", "selectEffectiveStatus": "Select effective status"}, "validation": {"priceCannotBeLessThanZero": "{{field}} cannot be less than 0", "scheduleFormatError": "Format: D+digits1-7, e.g. D123, digits cannot be repeated"}}, "expandedTable": {"title": "Density Price Interval Details", "addInterval": "Add Interval", "deleteConfirm": {"title": "Confirm Delete", "description": "Are you sure you want to delete this density price interval?", "okText": "Confirm", "cancelText": "Cancel"}}, "validation": {"pleaseInput": "Please input {{field}}!", "pleaseInputMPrice": "Please input M price!", "pleaseInputNPrice": "Please input N price!", "pleaseInputQ45Price": "Please input Q45 price!", "pleaseInputQ100Price": "Please input Q100 price!", "pleaseInputQ300Price": "Please input Q300 price!", "pleaseInputQ500Price": "Please input Q500 price!", "pleaseInputQ1000Price": "Please input Q1000 price!", "scheduleStartWithD": "Please start with letter D", "scheduleOnlyNumbers1to7": "Only digits 1-7 are allowed after D", "scheduleNoDuplicateNumbers": "Digits cannot be repeated"}, "pagination": {"total": "Total {{total}} records"}, "actionModal": {"title": {"add": "Add International Price", "edit": "Edit International Price"}, "buttons": {"back": "Back", "reset": "Reset", "cancel": "Cancel", "save": "Save", "delete": "Delete", "addPackageCharge": "Add Package Charge", "addSpecialCharge": "Add Special Charge", "addIntervalPrice": "Add Interval Price", "addOtherFee": "Add Other Fee", "addCabinReport": "Add Cabin Report", "addPriceChange": "Add Price Change", "addSpecialPrice": "Add Special Price"}, "sections": {"basicInfo": "Basic Information", "routeInfo": "Route Information", "densityAndSize": "Size and Weight Limits", "intervalPricing": "Density Price Intervals", "specialItems": "Special Items Configuration", "priceInfo": "Price Information", "packageCharges": "Package Charges", "specialCharges": "Special Item Charges", "tieredPricing": "Tiered Pricing", "smallCargoFee": "Small Cargo Fee", "branchPricing": "Branch Pricing", "otherFees": "Other Fees", "cabinReport": "Cabin Report", "priceChanges": "Price Changes", "specialPrice": "Special Price"}, "fields": {"supplier": "Supplier", "effectiveDate": "Effective Date", "isEffective": "Is Effective", "acceptSanction": "Accept Sanction", "airlineName": "Airline", "originPort": "Origin Port", "originSchedules": "Origin Schedules", "destinationPort": "Destination Port", "isTransfer": "Is Transfer", "transferPort": "Transfer Port", "transferSchedules": "Transfer Schedules", "minDensity": "<PERSON>", "maxDensity": "<PERSON>", "lengthLimit": "Length Limit", "widthLimit": "Width Limit", "heightLimit": "Height Limit", "isCabin": "Is Cabin Available", "cabinPrice": "<PERSON><PERSON><PERSON>", "mPrice": "<PERSON> Price", "nPrice": "N Price", "q45Price": "Q45 Price", "q100Price": "Q100 Price", "q300Price": "Q300 Price", "q500Price": "Q500 Price", "q1000Price": "Q1000 Price", "smallCargoFee": "Small Cargo Fee", "q45Sub": "M, N to Q45 Convergence", "subQ45Price": "Sub Q45 Price", "q100Sub": "Q45 to Q100 Convergence", "subQ100Price": "Sub Q100 Price", "packageItemName": "Package Item Name", "chargeValue": "Charge Value", "specialItemName": "Special Item Name", "cabinReportItem": "Cabin Report", "cabinDate": "Date", "transferDate": "Transfer Departure Date", "weightLimit": "Weight Limit", "volumeLimit": "Volume Limit", "lowerDensity": "Lower Density", "upperDensity": "Upper Density", "onlyParts": "Only Parts", "singleInquiry": "Single Inquiry", "leftDensity": "Left Density", "rightDensity": "Right Density", "priceChange": "Price Change", "densityUpperLimit": "Density Upper Limit", "densityLowerLimit": "Density Lower Limit", "specialPrice": "Special Price", "densityRange": "Density Range", "singleWeightLimit": "Single Weight Limit", "acceptBrand": "Accept Brand Goods", "feeName": "Fee Name", "billingMethod": "Billing Method", "orderFees": "Per Order Fee", "weightFees": "<PERSON>g <PERSON>", "minCharges": "Minimum Charge", "isCharges": "Default Charge"}, "placeholders": {"inputSupplier": "Please input supplier", "selectSupplier": "Please select supplier", "selectAirline": "Please select airline", "selectOriginPort": "Please select origin port", "selectOriginSchedules": "Please select origin schedules", "selectDestinationPort": "Please select destination port", "selectTransferPort": "Please select transfer port", "selectTransferSchedules": "Please select transfer schedules", "inputMinDensity": "Please input min density", "inputMaxDensity": "Please input max density", "inputLengthLimit": "Please input length limit", "inputWidthLimit": "Please input width limit", "inputHeightLimit": "Please input height limit", "inputMPrice": "Please input M price", "inputNPrice": "Please input N price", "inputQ45Price": "Please input Q45 price", "inputQ100Price": "Please input Q100 price", "inputQ300Price": "Please input Q300 price", "inputQ500Price": "Please input Q500 price", "inputQ1000Price": "Please input Q1000 price", "inputSmallCargoFee": "Please input small cargo fee", "inputSubQ45Price": "Please input sub Q45 price", "inputSubQ100Price": "Please input sub Q100 price", "packageItemName": "Package item name", "chargeValue": "Charge value", "specialItemName": "Special item name", "inputWeightLimit": "Please input weight limit, default 99999 means unlimited", "inputVolumeLimit": "Please input volume limit, default 99999 means unlimited", "inputLowerDensity": "Please input lower density, default 0", "inputUpperDensity": "Please input upper density, default 99999 means unlimited", "inputLeftDensity": "Please input left density", "inputRightDensity": "Please input right density", "inputPriceChange": "Please input price change, negative means decrease", "inputDensityUpperLimit": "Please input density upper limit", "inputDensityLowerLimit": "Please input density lower limit", "inputSpecialPrice": "Please input special price", "inputDensityLowerValue": "Please input density lower limit", "inputDensityUpperValue": "Please input density upper limit", "selectDates": "Please select dates", "minDensity": "<PERSON>", "maxDensity": "<PERSON>", "inputSingleWeightLimit": "Please input single weight limit", "singleWeightLimitHelp": "Default 99999kg means unlimited", "inputFeeName": "Please select fee name", "inputOrderFees": "Please input per order fee", "inputWeightFees": "Please input per kg fee", "inputMinCharges": "Please input minimum charge"}, "weekdays": {"all": "Select All", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "billingMethods": {"perOrder": "Per Order", "perKg": "Per Kg + Min Charge"}, "validation": {"selectEffectiveDate": "Please select effective date", "selectAirline": "Please select airline", "selectOriginPort": "Please select origin port", "selectOriginSchedules": "Please select origin schedules", "selectDestinationPort": "Please select destination port", "selectTransferPort": "Please select transfer port", "selectTransferSchedules": "Please select transfer schedules", "selectCabinDate": "Please select cabin date", "selectTransferDate": "Please select transfer departure date", "inputMinDensity": "Please input min density", "inputMaxDensity": "Please input max density", "inputQ100Price": "Please input Q100 price", "inputQ300Price": "Please input Q300 price", "inputQ500Price": "Please input Q500 price", "inputQ1000Price": "Please input Q1000 price", "inputLeftDensity": "Please input left density value", "inputRightDensity": "Please input right density value", "inputPriceChange": "Please input price change value", "inputDensityLowerLimit": "Please input density lower limit", "inputDensityUpperLimit": "Please input density upper limit", "inputSpecialPrice": "Please input special price", "inputFeeName": "Please input fee name", "inputOrderFees": "Please input per order fee", "inputWeightFees": "Please input per kg fee", "inputMinCharges": "Please input minimum charge"}, "messages": {"addSuccess": "Added successfully", "editSuccess": "Edited successfully", "addFailed": "Add failed", "editFailed": "Edit failed", "submitFailed": "Submit failed, please check network connection", "submitError": "Error occurred while submitting form", "validationError": "Form validation failed", "unknownError": "Unknown error"}}, "detailModal": {"title": "International Price Details", "sections": {"basicInfo": "Basic Information", "routeInfo": "Route Information", "cargoLimits": "Cargo Limits", "priceInfo": "Price Information", "priceTiers": "Price Tiers", "specialCharges": "Special Item Surcharges", "packageCharges": "Package Charges"}, "fields": {"supplierName": "Supplier Name", "createTime": "Create Time", "updateTime": "Update Time", "effectiveTime": "Effective Time", "isEffective": "Is Effective", "airline": "Airline", "originPort": "Origin Port", "originSchedules": "Origin Schedules", "destinationPort": "Destination Port", "isTransfer": "Is Transfer", "transferPort": "Transfer Port", "transferSchedules": "Transfer Schedules", "minDensity": "<PERSON>", "maxDensity": "<PERSON>", "lengthLimit": "Length Limit", "widthLimit": "Width Limit", "heightLimit": "Height Limit", "singleWeightLimit": "Single Weight Limit", "acceptBrand": "Accept Brand Goods", "isCabin": "Is Cabin Available", "cabinPrice": "<PERSON><PERSON><PERSON>", "smallCargoFee": "Small Cargo Fee"}, "priceTypes": {"mPrice": "<PERSON> Price", "nPrice": "N Price", "q45Price": "Q45 Price", "q100Price": "Q100 Price", "bulkPrice": "Bulk Price", "standardPrice": "Standard Price", "basePrice": "Base Price", "above45kg": "Above 45kg", "above100kg": "Above 100kg", "above300kg": "Above 300kg"}, "priceLabels": {"standard": "Standard", "base": "Base", "standardTier": "Standard", "segment": "Segment", "q300": "Q300", "q500": "Q500", "q1000": "Q1000"}, "booleanValues": {"yes": "Yes", "no": "No"}, "units": {"cny": "CNY", "cnyPerKg": "CNY/KG", "kgPerCubicMeter": "kg/m³"}, "noData": "-"}, "cabinReportEdit": {"title": "Edit Cabin Report"}}, "domesticPriceManagement": {"title": "Domestic Price Management", "newPrice": "New Price", "columns": {"shippingPlace": "Shipping Place", "shippingProvince": "Shipping Province", "destination": "Destination", "volumePrice": "Volume Price (CNY/m³)", "weightPrice": "Weight Price (CNY/kg)", "minCharge": "Min Charge (CNY)", "fastestAging": "Fastest Transit (days)", "slowestAging": "Slowest Transit (days)", "deliveryFee": "Delivery Fee (CNY)", "logistics": "Logistics", "actions": "Actions"}, "filters": {"inputLogisticsName": "Enter logistics name", "confirm": "Confirm", "reset": "Reset"}, "tooltips": {"viewDetail": "View Details", "edit": "Edit", "delete": "Delete"}, "deleteConfirm": {"title": "Delete Price", "description": "This operation will delete this price, continue?", "okText": "OK", "cancelText": "Cancel"}, "pagination": {"total": "Total {{total}} records"}, "messages": {"fetchListFailed": "Failed to fetch domestic price list", "deleteSuccess": "Price deleted successfully", "addSuccess": "Price added successfully", "editSuccess": "Price modified successfully", "addFailed": "Failed to add price", "editFailed": "Failed to modify price"}, "actionModal": {"title": {"add": "Add Domestic Price", "edit": "Edit Domestic Price"}, "sections": {"basicInfo": "Basic Information", "priceInfo": "Price Information", "agingInfo": "Transit Information"}, "fields": {"shippingPlace": "Shipping Place", "shippingProvince": "Shipping Province", "destination": "Destination", "volumePrice": "Volume Price", "weightPrice": "Weight Price", "minCharge": "Min Charge", "deliveryFee": "Delivery Fee", "logistics": "Logistics", "fastestAging": "Fastest Transit", "slowestAging": "Slowest Transit"}, "placeholders": {"selectShippingPlace": "Please select shipping place", "selectShippingProvince": "Please select shipping province", "selectDestination": "Please select destination", "inputVolumePrice": "Please enter volume price", "inputWeightPrice": "Please enter weight price", "inputMinCharge": "Please enter min charge", "inputDeliveryFee": "Please enter delivery fee", "inputLogistics": "Please enter logistics", "inputFastestAging": "Please enter fastest transit", "inputSlowestAging": "Please enter slowest transit"}, "validation": {"shippingPlaceRequired": "Please select shipping place!", "shippingProvinceRequired": "Please select shipping province!", "destinationRequired": "Please select destination!", "volumePriceRequired": "Please enter volume price!", "weightPriceRequired": "Please enter weight price!", "minChargeRequired": "Please enter min charge!", "deliveryFeeRequired": "Please enter delivery fee!", "logisticsRequired": "Please enter logistics!", "logisticsMaxLength": "Logistics cannot exceed 50 characters!", "fastestAgingRequired": "Please enter fastest transit!", "slowestAgingRequired": "Please enter slowest transit!"}, "units": {"yuanPerCubicMeter": "CNY/m³", "yuanPerKg": "CNY/kg", "yuan": "CNY", "days": "days"}}, "detailModal": {"title": "Domestic Price Details", "sections": {"basicInfo": "Basic Information", "priceInfo": "Price Information", "agingInfo": "Transit Information"}, "fields": {"shippingPlace": "Shipping Place", "shippingProvince": "Shipping Province", "destination": "Destination", "logistics": "Logistics Company", "volumePrice": "Volume Price", "weightPrice": "Weight Price", "minCharge": "Min Charge", "deliveryFee": "Delivery Fee", "fastestAging": "Fastest Transit", "slowestAging": "Slowest Transit"}, "booleanValues": {"yes": "Yes", "no": "No"}, "units": {"days": "days"}}}, "manualQuotation": {"title": "Special Quotation Management", "columns": {"inquiryId": "Inquiry ID", "freightMethod": "Freight Method", "goodsType": "Goods Type", "tradeTerms": "Trade Terms", "companyName": "Customer Name", "inquirer": "Inquirer", "inquiryTime": "Inquiry Time", "shippedPlace": "Shipped Place", "originPort": "Origin Port", "unloadingPort": "Destination Port", "grossWeight": "Gross Weight (KG)", "goodsVolume": "Goods Volume (m³)", "singleMaxWeight": "Single Max Weight (KG)", "isBrand": "Brand Goods", "ensureCabin": "Ensure Cabin", "isValidity": "Require ETD", "shipmentDate": "Shipment Date", "packageType": "Package Type", "specialCargo": "Special Cargo", "status": "Status", "actions": "Actions"}, "filters": {"search": "Search", "confirm": "Confirm", "reset": "Reset"}, "status": {"quoted": "Quoted", "unquoted": "Unquoted", "deal": "Deal", "nodeal": "No Deal", "unknown": "Unknown"}, "specialCargo": {"none": "None"}, "pagination": {"total": "Total {{total}} records"}, "messages": {"fetchListSuccess": "Manual quotation list fetched successfully", "fetchListFailed": "Failed to fetch manual quotation list"}, "quotationEditor": {"title": "Manual Quotation", "inquiryNumber": "Inquiry Number", "buttons": {"saveQuotation": "Save Quotation", "generateQuotation": "Generate Quotation", "cancelEdit": "Cancel Edit", "editContent": "Edit Content", "print": "Print", "export": "Export", "copy": "Copy"}, "tooltips": {"print": "Print quotation", "export": "Export as PDF file", "copy": "Copy to clipboard", "alignmentInfo": "Quotation content will be displayed in monospace font format to maintain alignment"}, "sections": {"quotationDetails": "Quotation Details", "quotationInfo": "Quotation Information", "basicInfo": "Basic Information", "priceInfo": "Price Information"}, "fields": {"quotationContent": "Quotation Content", "validityDate": "<PERSON>id <PERSON>", "airline": "Airline", "route": "Route", "transitTime": "Transit Time (days)", "schedule": "Schedule", "airFreight": "Air Freight (CNY/kg)", "chargeWeight": "Chargeable Weight (kg)", "terminalCharge": "Terminal Charge (USD/kg)", "terminalChargeMin": "Min Charge (USD)", "handleFee": "Handle Fee (USD/shpt)", "customs": "Customs Fee (USD/shpt)", "pickupFee": "Pickup <PERSON> (USD)", "airDocCharge": "Air Doc Charge (USD/shpt)", "ensCharge": "ENS Charge (USD/shpt)", "otherCharges": "Other Charges", "remarks": "Remarks"}, "placeholders": {"quotationContent": "Please enter quotation content", "airline": "Please enter airline", "route": "e.g.: PVG-LAX", "transitTime": "e.g.: 1-3", "schedule": "e.g.: ********", "airFreight": "Please enter air freight", "chargeWeight": "Please enter chargeable weight", "terminalCharge": "Please enter terminal charge", "terminalChargeMin": "Please enter min charge", "handleFee": "Please enter handle fee", "customs": "Please enter customs fee", "pickupFee": "Please enter pickup fee", "airDocCharge": "Please enter air doc charge", "ensCharge": "Please enter ENS charge", "otherCharges": "Please enter other charges", "remarks": "Please enter remarks"}, "validation": {"quotationContentRequired": "Quotation content cannot be empty", "validityDateRequired": "Please select validity date", "formValidationFailed": "Please check if the form is filled correctly"}, "messages": {"saveSuccess": "Quotation saved successfully", "saveFailed": "Save failed", "exportSuccess": "Quotation exported successfully", "copySuccess": "Quotation content copied to clipboard", "copyFailed": "Copy failed, please copy manually", "extractFieldsFailed": "Failed to extract fields"}}}, "organizationManagement": {"title": "Organization Management", "tabs": {"userManagement": "User Management", "departmentList": "Department List"}, "userManagement": {"title": "User List", "newUser": "New User", "columns": {"email": "Email", "fullname": "Full Name", "department": "Department", "identity": "Identity", "phone": "Phone", "status": "Status", "actions": "Actions"}, "status": {"enabled": "Enabled", "disabled": "Disabled", "systemUserCannotModify": "System user status cannot be modified"}, "actions": {"edit": "Edit", "delete": "Delete", "changePassword": "Change Password", "externalUserCannotEdit": "External user cannot be edited", "externalUserCannotDelete": "External user cannot be deleted", "externalUserCannotChangePassword": "External user password cannot be changed"}, "deleteConfirm": {"title": "Are you sure to delete this user?", "description": "This operation is irreversible, please operate with caution.", "okText": "Confirm", "cancelText": "Cancel"}, "messages": {"userEnabled": "User enabled", "userDisabled": "User disabled", "enableFailed": "Enable failed", "disableFailed": "Disable failed", "deleteSuccess": "User deleted successfully", "deleteFailed": "Delete failed", "updateSuccess": "User updated successfully", "updateFailed": "Update failed", "addSuccess": "User added successfully", "addFailed": "Add failed", "saveError": "Error saving user", "enableError": "Error enabling user", "disableError": "Error disabling user", "deleteError": "Error deleting user"}, "pagination": {"total": "Total {{total}} records"}, "placeholders": {"searchUser": "Search username, name or contact", "selectUserIdentity": "Please select user identity"}, "userIdentity": {"0": "External User", "1": "Inquiry Sales", "2": "Price Sales", "3": "Inquiry Manager", "4": "Price Manager", "5": "Super Admin"}, "userFormModal": {"title": {"add": "Add User", "edit": "Edit User"}, "fields": {"email": "Email", "password": "Password", "fullname": "Full Name", "department": "Department", "phone": "Phone", "userIdentity": "User Identity", "status": "Status", "remarks": "Remarks"}, "placeholders": {"email": "Please enter email", "password": "Please enter password", "passwordEdit": "Leave blank to keep current password", "fullname": "Please enter full name", "department": "Please select department", "phone": "Please enter phone", "remarks": "Please enter remarks"}, "validation": {"emailRequired": "Please enter username", "emailInvalid": "Please enter a valid email address", "emailMaxLength": "Email cannot exceed 100 characters", "passwordRequired": "Please enter password", "passwordMaxLength": "Password cannot exceed 50 characters", "fullnameMaxLength": "Full name cannot exceed 100 characters", "departmentRequired": "Please select department", "phoneMaxLength": "Phone cannot exceed 100 characters", "userIdentityRequired": "Please select user identity", "remarksMaxLength": "Remarks cannot exceed 200 characters"}, "tooltips": {"passwordEdit": "Leave blank to keep current password"}, "status": {"enabled": "Enabled", "disabled": "Disabled"}}}, "departmentManagement": {"title": "Department List", "newDepartment": "New Department", "columns": {"departmentId": "Department ID", "departmentName": "Department Name", "actions": "Actions"}, "actions": {"edit": "Edit", "delete": "Delete"}, "deleteConfirm": {"title": "Are you sure to delete this department?", "description": "This operation is irreversible, please operate with caution.", "okText": "Confirm", "cancelText": "Cancel"}, "messages": {"deleteSuccess": "Department deleted successfully", "addSuccess": "Department added successfully", "updateSuccess": "Department updated successfully", "addFailed": "Department add failed", "updateFailed": "Department update failed"}, "departmentFormModal": {"title": {"add": "Add Department", "edit": "Edit Department"}, "fields": {"departmentId": "Department ID", "departmentName": "Department Name"}, "placeholders": {"departmentName": "Please enter department name"}, "validation": {"departmentNameRequired": "Please enter department name", "departmentNameMaxLength": "Department name cannot exceed 50 characters"}}}}, "quotationEditor": {"title": "Air Freight Quotation", "buttons": {"save": "Save Quotation", "edit": "Edit Content", "cancel": "Cancel Edit", "close": "Close", "print": "Print", "export": "Export", "copy": "Copy", "reset": "Reset", "confirmAdjustment": "Confirm Adjustment", "cancelAdjustment": "Cancel Adjustment"}, "sections": {"quotationDetails": "Quotation Details", "quotationContent": "Quotation Content", "priceAdjustment": "Price Adjustment", "priceDetails": "Price Details", "additionalCharges": "Additional Charges", "additionalFees": "Additional Fees", "routeInfo": "Route Information", "cargoLimits": "Cargo Restrictions", "priceInfo": "Price Information", "priceGradient": "<PERSON> Gradient", "specialCharges": "Special Item Surcharges", "packageCharges": "Packaging Charges"}, "fields": {"department": "Department", "priceId": "Price ID", "airline": "Airline", "originPort": "Origin Port", "destinationPort": "Destination Port", "originSchedules": "Origin Schedules", "isTransfer": "Is Transfer", "transferPort": "Transfer Port", "transferSchedules": "Transfer Schedules", "minDensity": "<PERSON>", "maxDensity": "<PERSON>", "lengthLimit": "Length Limit", "widthLimit": "Width Limit", "heightLimit": "Height Limit", "canEnsureCabin": "Space Priority", "cabinPrice": "<PERSON><PERSON><PERSON>", "smallCargoFee": "Small Cargo Fee", "totalPrice": "Total Price", "originalPrice": "Original Price", "adjustedPrice": "Adjusted Price", "profit": "Profit", "mPrice": "<PERSON> Price", "nPrice": "N Price", "q45Price": "Q45 Price", "q100Price": "Q100 Price", "q300Price": "Q300 Price", "q500Price": "Q500 Price", "q1000Price": "Q1000 Price", "terminalCharge": "Terminal Charge", "handleFee": "Handle Fee", "customsFee": "Customs Fee", "pickingFee": "Picking Fee", "docCharge": "Doc Charge"}, "priceTypes": {"mPrice": "<PERSON> Price", "nPrice": "N Price", "q45Price": "Q45 Price", "q100Price": "Q100 Price", "bulkPrice": "Bulk Price", "standardPrice": "Standard Price", "basePrice": "Base Price", "above45kg": "Above 45kg", "above100kg": "Above 100kg", "above300kg": "Above 300kg"}, "priceLabels": {"standard": "Standard", "base": "Base", "standardTier": "Standard", "segment": "Segment", "q300": "Q300", "q500": "Q500", "q1000": "Q1000"}, "booleanValues": {"yes": "Yes", "no": "No"}, "units": {"cny": "CNY", "cnyPerKg": "CNY/KG", "kgPerCubicMeter": "kg/m³", "days": "days"}, "placeholders": {"quotationContent": "Please enter quotation content"}, "tooltips": {"quotationFormat": "Quotation content will be displayed in monospace font format to maintain alignment"}, "validation": {"quotationContentRequired": "Quotation content cannot be empty"}, "messages": {"saveSuccess": "Quotation saved successfully", "copySuccess": "Quotation content copied to clipboard", "copyFailed": "Copy failed, please copy manually", "validationFailed": "Please check if the form is filled correctly", "resetSuccess": "Prices have been reset to original values", "priceAdjustmentConfirmed": "Price adjustment confirmed", "priceAdjustmentCanceled": "Price adjustment canceled"}, "additionalFees": {"title": "Additional Fees", "terminalCharge": "Terminal Charge", "handleFee": "Handle Fee", "customsFee": "Customs Fee", "pickupFee": "Pick up Fee", "docCharge": "Air Doc Charge", "ensCharge": "ENS Charge", "brandCost": "Brand Cost", "carPaking": "Car Parking", "smallCargoFee": "Small Cargo Fee", "none": "None"}, "quotationTemplate": {"airline": "Airline（航空公司）", "route": "Route（路线）", "transitTime": "T/T（时效）", "schedule": "Schedule（班次频率）", "airFreight": "A/F (运费单价)", "chargeableWeight": "CW（计费重）", "additionalFees": "额外费用"}}, "dataManagement": {"airlineManagement": {"title": "Airline Management", "newAirline": "New Airline", "columns": {"cnAirlineName": "Chinese Airline Name", "enAirlineName": "English Airline Name", "actions": "Actions"}, "actions": {"edit": "Edit", "delete": "Delete"}, "deleteConfirm": {"title": "Delete Airline", "description": "This operation will delete the airline, continue?", "okText": "OK", "cancelText": "Cancel"}, "messages": {"deleteSuccess": "Airline deleted successfully", "addSuccess": "Airline added successfully", "updateSuccess": "Airline updated successfully", "addFailed": "Airline add failed", "updateFailed": "Airline update failed"}, "modal": {"title": {"add": "New Airline", "edit": "Edit Airline"}, "fields": {"id": "ID", "cnAirlineName": "Chinese Airline Name", "enAirlineName": "English Airline Name"}, "placeholders": {"cnAirlineName": "Please enter Chinese airline name", "enAirlineName": "Please enter English airline name"}, "validation": {"cnAirlineNameRequired": "Please enter Chinese airline name!", "cnAirlineNameMaxLength": "Chinese airline name cannot exceed 100 characters!", "enAirlineNameRequired": "Please enter English airline name!", "enAirlineNameMaxLength": "English airline name cannot exceed 100 characters!"}}}, "harborManagement": {"title": "Harbor Management", "newHarbor": "New Harbor", "columns": {"cnPortName": "Chinese Port Name", "enPortName": "Industry Standard (English) Name", "actions": "Actions"}, "actions": {"edit": "Edit", "delete": "Delete"}, "deleteConfirm": {"title": "Delete Harbor", "description": "This operation will delete the harbor, continue?", "okText": "OK", "cancelText": "Cancel"}, "messages": {"deleteSuccess": "Harbor deleted successfully", "addSuccess": "Harbor added successfully", "updateSuccess": "Harbor updated successfully", "addFailed": "Harbor add failed", "updateFailed": "Harbor update failed"}, "modal": {"title": {"add": "New Harbor", "edit": "Edit Harbor"}, "fields": {"id": "ID", "cnPortName": "Chinese Port Name", "enPortName": "Industry Standard (English) Name"}, "placeholders": {"cnPortName": "Please enter Chinese port name", "enPortName": "Please enter industry standard (English) name"}, "validation": {"cnPortNameRequired": "Please enter Chinese port name", "cnPortNameMaxLength": "Chinese port name cannot exceed 100 characters", "enPortNameRequired": "Please enter industry standard (English) name", "enPortNameMaxLength": "Industry standard (English) name cannot exceed 100 characters"}}}, "packageTypeManagement": {"title": "Package Type Management", "newPackageType": "New Package Type", "columns": {"id": "ID", "packageName": "Package Name", "actions": "Actions"}, "actions": {"edit": "Edit", "delete": "Delete"}, "deleteConfirm": {"title": "Delete Package Type", "description": "This operation will delete the package type, continue?", "okText": "OK", "cancelText": "Cancel"}, "messages": {"deleteSuccess": "Package type deleted successfully", "addSuccess": "Package type added successfully", "updateSuccess": "Package type updated successfully", "addFailed": "Package type add failed", "updateFailed": "Package type update failed"}, "modal": {"title": {"add": "New Package Type", "edit": "Edit Package Type"}, "fields": {"id": "ID", "packageName": "Package Name"}, "placeholders": {"packageName": "Please enter package name"}, "validation": {"packageNameRequired": "Please enter package name!", "packageNameMaxLength": "Package name cannot exceed 100 characters!"}}}, "specialItemsManagement": {"title": "Special Items Management", "newSpecialItem": "New Special Item", "columns": {"id": "ID", "specialItemsName": "Special Items Name", "actions": "Actions"}, "actions": {"edit": "Edit", "delete": "Delete"}, "deleteConfirm": {"title": "Delete Special Item", "description": "This operation will delete the special item, continue?", "okText": "OK", "cancelText": "Cancel"}, "messages": {"deleteSuccess": "Special item deleted successfully", "addSuccess": "Special item added successfully", "updateSuccess": "Special item updated successfully", "addFailed": "Special item add failed", "updateFailed": "Special item update failed"}, "modal": {"title": {"add": "New Special Item", "edit": "Edit Special Item"}, "fields": {"id": "ID", "specialItemsName": "Special Items Name"}, "placeholders": {"specialItemsName": "Please enter special items name"}, "validation": {"specialItemsNameRequired": "Please enter special items name!", "specialItemsNameMaxLength": "Special items name cannot exceed 100 characters!"}}}, "shipmentPlaceManagement": {"title": "Shipment Place Management", "newShipmentPlace": "New Shipment Place", "columns": {"shipmentPlace": "Shipment Place", "enPlace": "English Place (Pinyin)", "shipmentProvince": "Province", "enProvince": "English Province (Pinyin)", "actions": "Actions"}, "actions": {"edit": "Edit", "delete": "Delete"}, "deleteConfirm": {"title": "Delete Shipment Place", "description": "This operation will delete the shipment place, continue?", "okText": "OK", "cancelText": "Cancel"}, "messages": {"deleteSuccess": "Shipment place deleted successfully", "addSuccess": "Shipment place added successfully", "updateSuccess": "Shipment place updated successfully", "addFailed": "Shipment place add failed", "updateFailed": "Shipment place update failed"}, "modal": {"title": {"add": "New Shipment Place", "edit": "Edit Shipment Place"}, "fields": {"id": "ID", "shipmentPlace": "Shipment Place", "enPlace": "English Place (Pinyin)", "shipmentProvince": "Province", "enProvince": "English Province (Pinyin)"}, "placeholders": {"shipmentPlace": "Please enter shipment place", "enPlace": "Please enter English place (Pinyin)", "shipmentProvince": "Please enter province", "enProvince": "Please enter English province (Pinyin)"}, "validation": {"shipmentPlaceRequired": "Please enter shipment place!", "shipmentPlaceMaxLength": "Shipment place cannot exceed 100 characters!", "enPlaceRequired": "Please enter English place (<PERSON>nyin)!", "enPlaceMaxLength": "English place (Pinyin) cannot exceed 100 characters!", "shipmentProvinceRequired": "Please enter province!", "shipmentProvinceMaxLength": "Province cannot exceed 100 characters!", "enProvinceRequired": "Please enter English province (Pinyin)!", "enProvinceMaxLength": "English province (Pinyin) cannot exceed 100 characters!"}}}, "supplierManagement": {"title": "Supplier Management", "newSupplier": "New Supplier", "columns": {"supplierName": "Supplier Name", "actions": "Actions"}, "actions": {"edit": "Edit", "delete": "Delete"}, "deleteConfirm": {"title": "Delete Supplier", "description": "This operation will delete the supplier, continue?", "okText": "OK", "cancelText": "Cancel"}, "messages": {"deleteSuccess": "Supplier deleted successfully", "addSuccess": "Supplier added successfully", "updateSuccess": "Supplier updated successfully", "addFailed": "Supplier add failed", "updateFailed": "Supplier update failed"}, "modal": {"title": {"add": "New Supplier", "edit": "Edit Supplier"}, "fields": {"id": "ID", "supplierName": "Supplier Name"}, "placeholders": {"supplierName": "Please enter supplier name"}, "validation": {"supplierNameRequired": "Please enter supplier name!", "supplierNameMaxLength": "Supplier name cannot exceed 100 characters!"}}}}, "quickAccess": {"title": "Quick Access to Base Data:", "goTo": "Go to"}}