import { useEffect, useState } from "react";
import "./index.less";
import SideLeft from "./components/SideLeft";
import {
  Form,
  message,
  Empty,
  Row,
  Col,
  Spin,
  Button,
  Select,
  DatePicker,
  Tooltip,
} from "antd";
import QuotationEditPage from "./components/QuotationEditPage";
import PriceCard from "./components/PriceCard";
import { getInternationalPriceByInquiry } from "./services";
import { useLocation } from "react-router-dom";
import { useAppSelector } from "@/store/hooks";
import useBaseData from "@/hooks/useBaseData";
import dayjs from "dayjs";
import { DollarOutlined, ClockCircleOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";

interface DataType {
  id: string;
  priceid: string;
  airlinename: string;
  originport: string;
  unloadingport: string;
  originschedules?: string;
  istransfer?: boolean;
  transfer?: string;
  transferschedules?: string;
  mindensity?: string | number;
  maxdensity?: string | number;
  lengthlimit?: string | number;
  widthlimit?: string | number;
  heightlimit?: string | number;
  iscabin?: boolean;
  cabinprice?: string | number;
  packagecharges?: any;
  key?: React.Key;
  totalprice?: string | number;
  afprice?: string | number;
  validity?: string | number;
  departuretime?: string;
  optionaldate?: number[];
  datevalidity?: { [key: string]: number };
  datetotalprice?: { [key: string]: number };
  dateafprice?: { [key: string]: number };
}

interface FilterSortState {
  sortField: string | null;
  selectedAirline: string | undefined;
  selectedDates: dayjs.Dayjs[];
  selectedOriginPorts: string[];
  selectedTransferType: string | undefined;
}
const SupplyPrice: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const location = useLocation();
  const [supplyPriceData, setSupplyPriceData] = useState<DataType[]>([]);
  const [selectQuotation, setSelectQuotation] = useState<any>({});
  const [pageMode, setPageMode] = useState<"list" | "edit">("list");
  const [currentPriceDetail, setCurrentPriceDetail] = useState<any>(null);
  const [tableLoading, setTableLoading] = useState(false);
  const [filterSortState, setFilterSortState] = useState<FilterSortState>({
    sortField: null,
    selectedAirline: undefined,
    selectedDates: [],
    selectedOriginPorts: [],
    selectedTransferType: undefined,
  });
  const [displayData, setDisplayData] = useState<DataType[]>([]);
  const [clickedPriceId, setClickedPriceId] = useState<string | null>(null);
  const { user } = useAppSelector((state) => state.user);

  const updateFilterSortState = (updates: Partial<FilterSortState>) => {
    setFilterSortState((prev) => ({ ...prev, ...updates }));
  };

  const applyCurrentFilters = (newState?: Partial<FilterSortState>) => {
    const currentState = newState
      ? { ...filterSortState, ...newState }
      : filterSortState;

    if (supplyPriceData.length > 0) {
      const filtered = applyFilterAndSort(
        supplyPriceData,
        currentState.selectedAirline,
        currentState.selectedDates,
        currentState.selectedOriginPorts,
        currentState.selectedTransferType,
        currentState.sortField
      );
      setDisplayData(filtered);
    }
  };

  const {
    airlineOptions: airlineList,
    portOptions: portList,
    packageTypeOptions: packageTypeList,
    specialItemOptions: specialItemsList,
    loadAll,
  } = useBaseData();

  const setSupplyPriceList = async (state: any) => {
    setTableLoading(true);
    try {
      const res = await getInternationalPriceByInquiry({
        ...state,
        userid: user?.userid,
        departmentid: user?.departmentid,
      });
      const { data } = res;
      if (data?.resultCode === 200) {
        setSupplyPriceData(data.data);
      } else {
        message.error(t("supplyPrice.getSupplyPriceListFailed"));
      }
      setTableLoading(false);
    } catch (e) {
      console.log(e);
      setSupplyPriceData([]);
    }
  };

  // 航司筛选
  const handleAirlineFilter = (airline: string | undefined) => {
    updateFilterSortState({ selectedAirline: airline });
  };

  // 时间筛选
  const handleDateFilter = (dates: dayjs.Dayjs[]) => {
    updateFilterSortState({ selectedDates: dates });
  };

  // 起始港筛选
  const handleOriginPortFilter = (originPorts: string[]) => {
    updateFilterSortState({ selectedOriginPorts: originPorts });
  };

  // 航班中转筛选
  const handleTransferTypeFilter = (transferType: string | undefined) => {
    updateFilterSortState({ selectedTransferType: transferType });
  };

  const formatDates = (dates: dayjs.Dayjs[]) =>
    dates.map((date) => dayjs(date).format("YYYY-MM-DD"));

  const getDateBasedValue = (
    item: DataType,
    dateFieldKey: keyof DataType,
    fallbackKey: keyof DataType,
    selectedDates?: string[]
  ): number => {
    if (selectedDates && selectedDates.length > 0) {
      const firstDate = selectedDates[0];
      const dateMap = item[dateFieldKey] as Record<string, number> | undefined;
      if (dateMap?.[firstDate] !== undefined) {
        return parseInt(String(dateMap[firstDate]), 10);
      }
    }

    const dateMap = item[dateFieldKey] as Record<string, number> | undefined;
    if (dateMap && typeof dateMap === "object") {
      const firstDate = Object.keys(dateMap)[0];
      if (firstDate) {
        return parseInt(String(dateMap[firstDate]), 10);
      }
    }

    return parseInt(String(item[fallbackKey] ?? "999"), 10);
  };
  const applyFilterAndSort = (
    data: DataType[],
    airlineFilter?: string,
    dateFilter?: dayjs.Dayjs[],
    originPortFilter?: string[],
    transferTypeFilter?: string,
    sortType?: string | null
  ): DataType[] => {
    let result = [...data];
    const selectedDates = dateFilter ? formatDates(dateFilter) : [];

    // 航司筛选
    if (airlineFilter) {
      result = result.filter((item) => item.airlinename === airlineFilter);
    }

    // 起始港筛选
    if (originPortFilter?.length) {
      result = result.filter((item) =>
        originPortFilter.includes(item.originport)
      );
    }

    // 航班中转筛选
    if (transferTypeFilter) {
      result = result.filter((item) =>
        transferTypeFilter === "direct"
          ? !item.istransfer
          : transferTypeFilter === "transfer"
            ? item.istransfer
            : true
      );
    }

    // 时间筛选
    if (selectedDates.length > 0) {
      result = result.filter((item) => {
        const optionalDates =
          item.optionaldate?.map((d) => dayjs(d).format("YYYY-MM-DD")) || [];
        return selectedDates.some((date) => optionalDates.includes(date));
      });
    }

    // 排序
    if (sortType === "price") {
      result.sort(
        (a, b) =>
          getDateBasedValue(a, "datetotalprice", "totalprice", selectedDates) -
          getDateBasedValue(b, "datetotalprice", "totalprice", selectedDates)
      );
    } else if (sortType === "validity") {
      result.sort(
        (a, b) =>
          getDateBasedValue(a, "datevalidity", "validity", selectedDates) -
          getDateBasedValue(b, "datevalidity", "validity", selectedDates)
      );
    } else if (sortType === "optionaldate") {
      result.sort((a, b) => {
        const aTime = a?.optionaldate?.[0];
        const bTime = b?.optionaldate?.[0];

        if (typeof aTime !== "number") return 1;
        if (typeof bTime !== "number") return -1;
        return aTime - bTime;
      });
    }

    return result;
  };
  const handleSort = (field: string) => {
    updateFilterSortState({ sortField: field });
  };

  const resetSort = () => {
    updateFilterSortState({ sortField: null });
    setDisplayData([]);
  };

  useEffect(() => {
    const state = location.state;
    if (state) {
      setSelectQuotation(state);
      form.setFieldsValue({
        ...state,
        originport: state?.originport?.split("/"),
        specialcargo: state?.specialcargo?.split(","),
        shipmentdate: state?.shipmentdate
          ? dayjs(state.shipmentdate, "YYYY-MM-DD")
          : undefined,
      });
      updateFilterSortState({
        selectedOriginPorts: state?.originport?.split("/"),
      });
      setSupplyPriceList(state);
    }
    loadAll();
  }, []);

  useEffect(() => {
    applyCurrentFilters();
  }, [filterSortState, supplyPriceData]);

  useEffect(() => {
    if (supplyPriceData.length > 0) {
      updateFilterSortState({ sortField: "price" });
    } else {
      resetSort();
      setDisplayData([]);
    }
  }, [supplyPriceData]);

  // 从编辑页面返回时滚动到记录的数据项位置
  useEffect(() => {
    if (pageMode === "list" && clickedPriceId && displayData.length > 0) {
      setTimeout(() => {
        const targetElement = document.querySelector(
          `[data-price-id="${clickedPriceId}"]`
        );
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
        }
      }, 100);
    }
  }, [pageMode, clickedPriceId, displayData]);

  const handleGenerate = (priceData: any, selectedDate?: string) => {
    setClickedPriceId(priceData?.priceid || null);
    setCurrentPriceDetail(priceData);
    setPageMode("edit");
  };

  const handleBackToList = () => {
    setPageMode("list");
    setCurrentPriceDetail(null);
  };

  const selectDates = filterSortState.selectedDates
    ?.map((item) => item?.format("MM-DD"))
    ?.join(", ");

  return (
    <>
      {pageMode === "edit" ? (
        <QuotationEditPage
          onBack={handleBackToList}
          supplyPriceId={currentPriceDetail?.ipricecode}
          priceData={currentPriceDetail}
          quoteData={selectQuotation}
        />
      ) : (
        <div className="supply_price">
          <div className="supply_price_left">
            <SideLeft
              form={form}
              selectQuotation={selectQuotation}
              setSelectQuotation={setSelectQuotation}
              setSupplyPriceList={setSupplyPriceList}
              portList={portList}
              packageTypeList={packageTypeList}
              specialItemsList={specialItemsList}
              airlineList={airlineList}
            />
          </div>
          <div className="supply_price_right">
            <div className="table_top">
              <div className="table_title">{t("supplyPrice.title")}</div>
              <div className="filter-sort-controls">
                {/* 航司筛选 */}
                <Select
                  placeholder={t("supplyPrice.selectAirline")}
                  allowClear
                  style={{ minWidth: 100, maxWidth: 140 }}
                  value={filterSortState.selectedAirline}
                  onChange={handleAirlineFilter}
                  options={airlineList.map((airline) => ({
                    label: airline.label,
                    value: airline.value,
                  }))}
                />
                {/* 起始港筛选 */}
                <Tooltip
                  title={filterSortState.selectedOriginPorts?.join(", ")}
                >
                  <Select
                    mode="multiple"
                    placeholder={t("supplyPrice.selectOriginPorts")}
                    allowClear
                    style={{ minWidth: 120, maxWidth: 180 }}
                    value={filterSortState.selectedOriginPorts}
                    onChange={handleOriginPortFilter}
                    options={portList.map((port) => ({
                      label: port.label,
                      value: port.value,
                    }))}
                    maxTagCount="responsive"
                  />
                </Tooltip>
                {/* 时间筛选 */}
                <Tooltip title={selectDates}>
                  <DatePicker
                    multiple
                    placeholder={t("supplyPrice.selectDates")}
                    style={{ minWidth: 120, maxWidth: 180 }}
                    value={filterSortState.selectedDates}
                    onChange={(dates) => handleDateFilter(dates || [])}
                    format="MM-DD"
                    needConfirm
                    maxTagCount="responsive"
                  />
                </Tooltip>
                {/* 航班中转筛选 */}
                <Select
                  placeholder={t("supplyPrice.selectTransferType")}
                  allowClear
                  style={{ minWidth: 120, maxWidth: 140 }}
                  value={filterSortState.selectedTransferType}
                  onChange={handleTransferTypeFilter}
                  options={[
                    {
                      label: t("supplyPrice.transferTypes.direct"),
                      value: "direct",
                    },
                    {
                      label: t("supplyPrice.transferTypes.transfer"),
                      value: "transfer",
                    },
                  ]}
                />
                {/* 排序按钮 */}
                <div className="sort-buttons">
                  <Button
                    type={
                      filterSortState.sortField === "price"
                        ? "primary"
                        : "default"
                    }
                    icon={<DollarOutlined />}
                    onClick={() => handleSort("price")}
                    className="sort-button"
                    size="small"
                  >
                    {t("supplyPrice.lowestPrice")}
                  </Button>
                  <Button
                    type={
                      filterSortState.sortField === "validity"
                        ? "primary"
                        : "default"
                    }
                    icon={<ClockCircleOutlined />}
                    onClick={() => handleSort("validity")}
                    className="sort-button"
                    size="small"
                  >
                    {t("supplyPrice.fastestTransit")}
                  </Button>
                  <Button
                    type={
                      filterSortState.sortField === "optionaldate"
                        ? "primary"
                        : "default"
                    }
                    icon={<ClockCircleOutlined />}
                    onClick={() => handleSort("optionaldate")}
                    className="sort-button"
                    size="small"
                  >
                    {t("supplyPrice.fastestETD")}
                  </Button>
                </div>
              </div>
            </div>

            <div className="price-cards-container">
              {tableLoading ? (
                <div className="loading-container">
                  <Spin size="large" tip={t("supplyPrice.loadingMessage")} />
                </div>
              ) : displayData.length > 0 ? (
                <>
                  <Row gutter={[0, 16]}>
                    {displayData.map((item) => (
                      <Col xs={24} sm={24} md={24} key={item.priceid}>
                        <PriceCard
                          item={item}
                          quoteData={selectQuotation}
                          onGenerateQuotation={handleGenerate}
                        />
                      </Col>
                    ))}
                  </Row>
                </>
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={t("supplyPrice.noDataMessage")}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SupplyPrice;
